do {
    func greet(person: String) -> String {
        let greeting = "Hello, " + person + "!"
        return greeting
    }
    func greetAgain(person: String) -> String {
        return "Hello again, " + person + "!"
    }
    print(greetAgain(person: "<PERSON>"))

    func greet(person: String, alreadyGreeted: <PERSON><PERSON>) -> String {
        if alreadyGreeted {
            return greetAgain(person: person)
        } else {
            return greet(person: person)
        }
    }
    print(greet(person: "Tim", alreadyGreeted: true))
}

do {
    func printAndCount(string: String) -> Int {
        print(string)
        return string.count
    }
    func printWithoutCounting(string: String) {
        let _ = printAndCount(string: string)
    }

    let count = printAndCount(string: "hello, world")
    print("\(count)")
    printWithoutCounting(string: "hello, world")
}
